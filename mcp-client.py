import os
import async<PERSON>
import json
from contextlib import AsyncExitStack
from typing import Any, Dict, List

import nest_asyncio
from dotenv import load_dotenv
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client
import asyncio
import google.generativeai as genai

# Apply nest_asyncio to allow nested event loops (needed for Jupyter/IPython)
nest_asyncio.apply()

# Load environment variables
load_dotenv()

# Configure Gemini
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

# Global variables to store session state
session = None
exit_stack = AsyncExitStack()
gemini_model = genai.GenerativeModel(os.getenv("GEMINI_MODEL_NAME"))
stdio = None
write = None


async def connect_to_server(server_script_path: str = "server.py"):
    """Connect to an MCP server.

    Args:
        server_script_path: Path to the server script.
    """
    global session, stdio, write, exit_stack

    # Server configuration
    server_params = StdioServerParameters(
        command="python",
        args=[server_script_path],
    )

    # Connect to the server
    stdio_transport = await exit_stack.enter_async_context(stdio_client(server_params))
    stdio, write = stdio_transport
    session = await exit_stack.enter_async_context(ClientSession(stdio, write))

    # Initialize the connection
    await session.initialize()

    # List available tools
    tools_result = await session.list_tools()
    print("\nConnected to server with tools:")
    for tool in tools_result.tools:
        print(f"  - {tool.name}: {tool.description}")


async def get_mcp_tools() -> List[Dict[str, Any]]:
    """Get available tools from the MCP server in Gemini format.

    Returns:
        A list of tools in Gemini format.
    """
    global session

    tools_result = await session.list_tools()
    return [
        {
            "type": "function",
            "function": {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.inputSchema,
            },
        }
        for tool in tools_result.tools
    ]


async def process_query(query: str) -> str:
    """Process a query using Gemini and available MCP tools.

    Args:
        query: The user query.

    Returns:
        The response from Gemini.
    """
    global session, gemini_model

    # Get available tools
    tools = await get_mcp_tools()

    # Create a prompt that includes tool information
    tools_description = "\n".join([
        f"- {tool['function']['name']}: {tool['function']['description']}"
        for tool in tools
    ])

    system_prompt = f"""You are a helpful assistant with access to the following tools:
{tools_description}

If you need to use a tool to answer the user's question, respond with a JSON object in this format:
{{"tool_call": {{"name": "tool_name", "arguments": {{"param1": "value1", "param2": "value2"}}}}}}

If you don't need to use any tools, just respond normally to the user's question.
"""

    # Initial Gemini API call
    full_prompt = f"{system_prompt}\n\nUser: {query}"
    response = gemini_model.generate_content(full_prompt)
    assistant_response = response.text

    # Check if the response contains a tool call
    try:
        # Try to parse as JSON to see if it's a tool call
        if assistant_response.strip().startswith('{"tool_call"'):
            tool_call_data = json.loads(assistant_response)
            tool_name = tool_call_data["tool_call"]["name"]
            tool_args = tool_call_data["tool_call"]["arguments"]

            # Execute tool call
            result = await session.call_tool(tool_name, arguments=tool_args)

            # Get final response from Gemini with tool results
            final_prompt = f"""Based on the tool result below, provide a helpful response to the user's question: "{query}"

Tool result: {result.content[0].text}

Provide a natural, helpful response based on this information."""

            final_response = gemini_model.generate_content(final_prompt)
            return final_response.text

    except (json.JSONDecodeError, KeyError):
        # Not a tool call, just return the direct response
        pass

    return assistant_response


async def cleanup():
    """Clean up resources."""
    global exit_stack
    await exit_stack.aclose()


async def main():
    """Main entry point for the client."""
    #await connect_to_server("server.py")
    async with sse_client("http://localhost:8050/sse") as (read_stream, write_stream):
        global session
        async with ClientSession(read_stream, write_stream) as session:
            # Initialize the connection
            await session.initialize()

            # List available tools
            tools_result = await session.list_tools()
            print("\nConnected to server with tools:")
            for tool in tools_result.tools:
                print(f"  - {tool.name}: {tool.description}")

            # Example: Ask about current time using Gemini
            query = "What is the current time?"
            print(f"\nQuery: {query}")

            response = await process_query(query)
            print(f"\nResponse: {response}")

            # Example: Extract entities from a query
            entity_query = "What events happened in Colombo, Sri Lanka on January 1st, 2023?"
            print(f"\nEntity Query: {entity_query}")
            entity_response = await session.call_tool(
                "extract_entities_tool", arguments={"query": entity_query}
            )
            print(f"\nEntity Response: {entity_response.content[0].text}")

            # Example: Refine a query
            refine_query = "What are the latest news about the president of Sri Lanka?"
            print(f"\nRefine Query: {refine_query}")
            refine_query_response = await session.call_tool(
                "refine_query_tool", arguments={"original_query": refine_query}
            )
            print(f"\nRefine Query Response: {refine_query_response.content[0].text}")

            # Example: Check relevance of a document to a query
            document = "This is a news article about the American tax implimentation."
            query = "Sri Lankan politics"
            print(f"\nRelevance Query: Document: '{document}', Query: '{query}'")
            relevance_response = await session.call_tool(
                "check_relevance", arguments={"text_chunk": document, "question": query}
            )
            print(f"\nRelevance Response: {relevance_response.content[0].text}")

    await cleanup()


if __name__ == "__main__":
    asyncio.run(main())
